# 触摸屏 MQTT 功能集成说明

## 概述

本文档说明了在 `touchmain.html` 文件中集成的 MQTT 功能，包括控制按钮集成、数据显示更新和状态指示器等功能。

## 功能特性

### 1. 控制按钮集成

在 `run-status-panel` 区域的三个控制按钮已集成 MQTT 功能：

- **启动按钮**: 点击时通过 MQTT 发送启动指令 `{"id": "A1", "value": "0"}`
- **停止按钮**: 点击时通过 MQTT 发送停止指令 `{"id": "A2", "value": "1"}`
- **复位按钮**: 点击时通过 MQTT 发送复位指令 `{"id": "A3", "value": "1"}`

### 2. 数据显示更新

#### 系统运行状态区域（5种状态显示）
- 通过 `MQTTElectricalDataManager` 类实时获取系统状态
- 支持的状态：就绪、运行、故障、备用、合高压等待
- 状态映射：
  - `HMI_30039_4`: 就绪状态
  - `HMI_30039_5`: 运行状态
  - `HMI_30039_6`: 故障状态
  - `HMI_30039_9`: 备用状态
  - `HMI_30039_10`: 合高压等待状态

#### 网侧负载无功电流图表区域
- 实时更新图表数据，使用 `HMI_32046` 参数
- 每5秒自动更新一次图表
- 支持最多60个数据点的历史显示

#### 系统关键参数区域（2-3列网格布局）
更新的参数包括：
- 母线电压Uab (`HMI_32030`)
- 母线电压Ubc (`HMI_32032`)
- 母线电压Uca (`HMI_32034`)
- SVG电流Ia (`HMI_32040`)
- SVG电流Ib (`HMI_32042`)
- SVG电流Ic (`HMI_32044`)
- 网侧负载无功电流 (`HMI_32046`)
- 负载无功功率 (`HMI_32048`)
- 功率因数 (`HMI_32050`)

### 3. 状态指示器

在 `footer-status-info` 区域显示 MQTT 连接状态和通信状态：

#### MQTT 连接状态指示器
- **已连接**: 绿色背景，显示 "MQTT: 已连接"
- **连接中**: 橙色背景，带脉冲动画，显示 "MQTT: 连接中..."
- **连接断开**: 红色背景，带脉冲动画，显示 "MQTT: 连接断开"

#### MQTT 数据接收状态指示器
- **接收中**: 蓝色背景，显示 "数据: 接收中"
- **等待中**: 灰色背景，显示 "数据: 等待中"
- **连接错误**: 红色背景，带脉冲动画，显示 "数据: 连接错误"

## 技术实现

### 核心组件

1. **MQTTElectricalDataManager**: 负责电气系统数据的 MQTT 连接和数据处理
2. **DeviceOperationManager**: 负责设备操作指令的 MQTT 发送
3. **触摸屏专用函数**: 
   - `updateTouchScreenFromMQTTData()`: 更新触摸屏界面
   - `updateTouchSystemStatus()`: 更新系统状态显示
   - `updateTouchKeyParameters()`: 更新关键参数显示

### 数据流程

1. MQTT 客户端连接到服务器 (`ws://*************:8083/mqtt`)
2. 订阅数据主题 (`/189/D19QBHKRZ791U/ws/service`)
3. 接收数据后通过回调函数更新界面
4. 用户操作通过控制主题发送指令

### 错误处理

- 自动重连机制：连接断开后5秒尝试重连
- 健康检查：每30秒检查一次连接状态
- 错误状态显示：在状态指示器中显示错误信息

## 使用方法

### 基本使用

1. 打开 `touchmain.html` 页面
2. 等待 MQTT 连接建立（状态指示器显示"已连接"）
3. 观察数据更新（参数值和图表会实时更新）
4. 使用控制按钮进行设备操作

### 调试功能

在浏览器控制台中可以使用以下调试函数：

```javascript
// 显示详细的 MQTT 功能调试信息
showTouchMQTTDebugInfo();

// 检查 MQTT 连接健康状态
checkTouchMQTTHealth();

// 手动更新连接状态
updateTouchMQTTConnectionStatus('connected', 'MQTT: 已连接');

// 手动更新数据状态
updateTouchMQTTDataStatus('receiving', '数据: 接收中');
```

### 测试页面

可以使用 `mqtt-test.html` 页面进行功能测试：

1. 打开 `webgl/mqtt-test.html`
2. 依次点击各个测试按钮
3. 查看测试结果和状态信息

## 配置说明

### MQTT 连接配置

- **服务器地址**: `ws://*************:8083/mqtt`
- **用户名**: `FastBee`
- **密码**: JWT Token（在代码中已配置）
- **连接超时**: 60秒
- **保持连接**: 30秒

### 数据更新频率

- **图表更新**: 每5秒
- **状态检查**: 每5秒
- **健康检查**: 每30秒
- **设备状态检查**: 每10秒

## 故障排除

### 常见问题

1. **MQTT 连接失败**
   - 检查网络连接
   - 确认服务器地址和端口
   - 检查用户名和密码

2. **数据不更新**
   - 检查 MQTT 连接状态
   - 确认订阅主题正确
   - 查看浏览器控制台错误信息

3. **控制按钮无响应**
   - 检查设备操作管理器是否初始化
   - 确认 MQTT 客户端连接状态
   - 查看发送的指令格式

### 调试步骤

1. 打开浏览器开发者工具
2. 查看控制台日志信息
3. 使用调试函数检查状态
4. 使用测试页面验证功能

## 注意事项

1. 确保所有依赖脚本正确加载
2. MQTT 服务器必须支持 WebSocket 连接
3. 页面需要在支持 WebSocket 的现代浏览器中运行
4. 建议在本地网络环境中使用以获得最佳性能

## 更新日志

- **v1.0**: 初始版本，集成基本 MQTT 功能
- 控制按钮 MQTT 集成
- 数据显示实时更新
- 状态指示器显示
- 错误处理和重连机制
- 调试功能和测试页面
